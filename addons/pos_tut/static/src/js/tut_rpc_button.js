odoo.define('pos_tut.tutRPCButton', function (require) {
    'use strict';

    const PosComponent = require('point_of_sale.PosComponent');
    const ProductScreen = require('point_of_sale.ProductScreen');
    const Registries = require('point_of_sale.Registries');
    const { useListener } = require("@web/core/utils/hooks");

    class TutRPCButton extends PosComponent {
        setup() {
            super.setup();
            useListener('click', this.rpc_button_on_click);
        }
        // Retrieve current user groups and display them in a selection pop-up
        async rpc_button_on_click() {
            console.log('Started');
            const results = await this.rpc({
                model: 'res.users',
                method: 'search_read',
                args: [[['id','=',this.env.pos.user.id]],['groups_id']]
            })
            const groups = results[0].groups_id

            if (groups && groups.length > 0){
                try {
                    const gDetails = await this.rpc({
                        model: 'res.groups',
                        method: 'search_read',
                        args: [[['id','in',groups]],['name','category_id']]
                    });
                    console.log('details: ', gDetails)
                    const { payload: selectedOption } = await this.showPopup('SelectionPopup', {
                        title: 'Make you choise',
                        list: gDetails.map( g => ({
                            id: g.id,
                            label: g.name,
                            item: g
                            })
                        )
                    // Do something with the selected option
                    


                    });
                } catch (er) {
                    console.log(er)
                    await this.showPopup('ErrorPopup', {
                        title: 'Back-end Error',
                        body: er.message.data.message + '\nAsk a JS developer to fix it',
                        });
                }
            }
        }
    }

    TutRPCButton.template = 'tut_rpc_button';
    ProductScreen.addControlButton({
        component: TutRPCButton,
        // Show/Hide Tut Button based on pos.config show_rpc_button using 'condition' function
        // Load directly from pos.config without using session
        condition: function() {
            return this.env.pos.config.show_rpc_button;
        },
    });

    Registries.Component.add(TutRPCButton);
    return TutRPCButton;

    
});

