from odoo import models, fields, api


class PosConfig(models.Model):
    _inherit = 'pos.config'

    show_tut_button = fields.Bo<PERSON>an('Show/Hide Tut Button')
    show_numpad = fields.<PERSON><PERSON>an('Show/Hide numpad')
    show_rpc_button = fields.<PERSON><PERSON>an('Show/Hide RPC Button')

    
class PosSession(models.Model):
    _inherit = 'pos.session'


    def get_current_session_username(self):
        return self.user_id.id